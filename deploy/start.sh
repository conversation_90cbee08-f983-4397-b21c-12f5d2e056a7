#!/bin/bash

# RuoYi 应用启动脚本
# 作者: zhenxqin
# 日期: 2025-07-25

APP_NAME="ruoyi-admin"
APP_JAR="ruoyi-admin.jar"
APP_PORT=8080
JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 获取进程ID
get_pid() {
    ps -ef | grep $APP_JAR | grep -v grep | awk '{print $2}'
}

# 启动应用
start() {
    pid=$(get_pid)
    if [ -n "$pid" ]; then
        echo -e "${YELLOW}应用已经在运行中，PID: $pid${NC}"
        return 1
    fi
    
    echo -e "${GREEN}正在启动 $APP_NAME...${NC}"
    nohup java $JAVA_OPTS -jar $APP_JAR --server.port=$APP_PORT > app.log 2>&1 &
    
    sleep 3
    pid=$(get_pid)
    if [ -n "$pid" ]; then
        echo -e "${GREEN}$APP_NAME 启动成功，PID: $pid${NC}"
        echo -e "${GREEN}访问地址: http://134.175.97.207:$APP_PORT${NC}"
        echo -e "${GREEN}默认账号: admin / admin123${NC}"
    else
        echo -e "${RED}$APP_NAME 启动失败${NC}"
        return 1
    fi
}

# 停止应用
stop() {
    pid=$(get_pid)
    if [ -z "$pid" ]; then
        echo -e "${YELLOW}应用未运行${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}正在停止 $APP_NAME，PID: $pid${NC}"
    kill $pid
    
    # 等待进程结束
    for i in {1..10}; do
        sleep 1
        pid=$(get_pid)
        if [ -z "$pid" ]; then
            echo -e "${GREEN}$APP_NAME 已停止${NC}"
            return 0
        fi
    done
    
    # 强制杀死进程
    echo -e "${RED}强制停止 $APP_NAME${NC}"
    kill -9 $pid
}

# 重启应用
restart() {
    stop
    sleep 2
    start
}

# 查看状态
status() {
    pid=$(get_pid)
    if [ -n "$pid" ]; then
        echo -e "${GREEN}$APP_NAME 正在运行，PID: $pid${NC}"
        echo -e "${GREEN}访问地址: http://134.175.97.207:$APP_PORT${NC}"
    else
        echo -e "${RED}$APP_NAME 未运行${NC}"
    fi
}

# 查看日志
logs() {
    if [ -f "app.log" ]; then
        tail -f app.log
    else
        echo -e "${RED}日志文件不存在${NC}"
    fi
}

# 主函数
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    *)
        echo "使用方法: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动应用"
        echo "  stop    - 停止应用"
        echo "  restart - 重启应用"
        echo "  status  - 查看状态"
        echo "  logs    - 查看日志"
        exit 1
        ;;
esac

exit 0
