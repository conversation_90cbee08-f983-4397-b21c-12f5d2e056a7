# RuoYi 项目部署指南

## 📋 服务器信息

- **IP 地址**: **************
- **用户**: root
- **密码**: Q942817264q
- **端口**: 8080

## 🚀 部署步骤

### 1. 连接服务器

```bash
ssh root@**************
```

### 2. 安装环境

```bash
# 更新系统
yum update -y

# 安装 Java 17
yum install -y java-17-openjdk java-17-openjdk-devel

# 验证 Java 安装
java -version

# 安装 MySQL
yum install -y mysql-server mysql

# 启动 MySQL
systemctl start mysqld
systemctl enable mysqld

# 查看 MySQL 初始密码
grep 'temporary password' /var/log/mysqld.log
```

### 3. 配置 MySQL

```bash
# 登录 MySQL
mysql -u root -p

# 在 MySQL 中执行以下命令:
ALTER USER 'root'@'localhost' IDENTIFIED BY 'Q942817264q';
CREATE DATABASE ry CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ry;
SOURCE /opt/ry_20250416.sql;
SOURCE /opt/quartz.sql;
EXIT;
```

### 4. 创建应用目录

```bash
mkdir -p /opt/ruoyi/logs
mkdir -p /opt/ruoyi/uploadPath
cd /opt/ruoyi
```

### 5. 上传文件

需要上传以下文件到 `/opt/ruoyi/` 目录：

- `ruoyi-admin.jar` (应用程序)
- `start.sh` (启动脚本)
- `application-prod.yml` (生产环境配置)

### 6. 设置权限

```bash
chmod +x /opt/ruoyi/start.sh
```

### 7. 启动应用

```bash
cd /opt/ruoyi
./start.sh start
```

### 8. 查看状态

```bash
./start.sh status
```

### 9. 查看日志

```bash
./start.sh logs
```

## 🔧 管理命令

```bash
# 启动应用
./start.sh start

# 停止应用
./start.sh stop

# 重启应用
./start.sh restart

# 查看状态
./start.sh status

# 查看日志
./start.sh logs
```

## 🌐 访问系统

- **系统地址**: http://**************:8080
- **默认账号**: admin
- **默认密码**: admin123
- **数据库监控**: http://**************:8080/druid
  - 用户名: ruoyi
  - 密码: 123456

## 🔥 防火墙配置

如果无法访问，需要开放端口：

```bash
# CentOS 7/8
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --reload

# 或者关闭防火墙 (测试环境可以临时关闭)
systemctl stop firewalld
systemctl disable firewalld
```

**注意：**

- 本方案使用 Spring Boot 内嵌 Tomcat，无需安装 Nginx
- 直接通过 8080 端口访问应用

## 📝 常见问题

### 1. 端口被占用

```bash
# 查看端口占用
netstat -tlnp | grep 8080

# 杀死进程
kill -9 <PID>
```

### 2. 内存不足

```bash
# 查看内存使用
free -h

# 修改 start.sh 中的 JAVA_OPTS
JAVA_OPTS="-Xms256m -Xmx512m -XX:+UseG1GC"
```

### 3. 数据库连接失败

```bash
# 检查 MySQL 状态
systemctl status mysqld

# 重启 MySQL
systemctl restart mysqld
```

## 📊 监控和维护

### 系统监控

```bash
# 查看系统资源
top
htop
df -h
free -h
```

### 应用监控

```bash
# 查看应用进程
ps aux | grep ruoyi

# 查看端口监听
netstat -tlnp | grep 8080

# 查看应用日志
tail -f /opt/ruoyi/logs/ruoyi.log
tail -f /opt/ruoyi/app.log
```

## 🔄 更新部署

1. 停止应用: `./start.sh stop`
2. 备份旧版本: `cp ruoyi-admin.jar ruoyi-admin.jar.bak`
3. 上传新版本
4. 启动应用: `./start.sh start`

---

**部署完成后，请访问 http://**************:8080 验证系统是否正常运行！**
